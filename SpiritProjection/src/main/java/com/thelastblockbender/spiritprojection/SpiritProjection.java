package com.thelastblockbender.spiritprojection;

import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import com.denizenscript.denizen.objects.PlayerTag;
import com.denizenscript.denizencore.objects.Mechanism;
import com.denizenscript.denizencore.objects.core.ElementTag;

import net.citizensnpcs.api.CitizensAPI;
import net.citizensnpcs.api.npc.NPC;
import net.citizensnpcs.api.npc.NPCRegistry;
import net.citizensnpcs.trait.SkinTrait;
import net.citizensnpcs.trait.SitTrait;

import java.util.HashMap;
import java.util.Map;

import com.projectkorra.projectkorra.ProjectKorra;
import com.projectkorra.projectkorra.TLBMethods;
import com.projectkorra.projectkorra.ability.AddonAbility;
import com.projectkorra.projectkorra.ability.SpiritualAbility;
import com.projectkorra.projectkorra.configuration.ConfigManager;
import com.projectkorra.projectkorra.util.ParticleEffect;
import com.projectkorra.projectkorra.util.StatisticsMethods;

public class SpiritProjection extends SpiritualAbility implements AddonAbility {

    // Static map to track NPCs and their associated players
    private static final Map<NPC, Player> npcToPlayerMap = new HashMap<>();

    // Configuration variables
    private long cooldown;
    private double maxDistance;
    private long maxDuration; // 3 seconds in milliseconds
    private long chargeTime; // Time required to charge the ability
    private double minSpeedPercent; // Minimum speed as percentage (0.1 = 0.1%)
    private float startingFlySpeed; // Starting fly speed for spirit projection
    private double maxVerticalRange; // Maximum vertical movement range in blocks
    private long currentLevel;

    // Runtime variables
    private Location startLocation;
    private GameMode originalGameMode;
    private long startTime;
    private long chargeStartTime;
    private boolean isActive;
    private boolean isCharging;
    private boolean isCharged;
    private float originalFlySpeed; // Store original fly speed to restore later
    private boolean hasDarknessEffect; // Track if darkness effect is currently applied
    private Location lastLocation; // Track previous location to determine movement direction
    private double lastDistance; // Track previous distance to determine if moving away or towards
    private NPC bodyNPC; // NPC representing the player's body at start location
    
    public SpiritProjection(Player player) {
        super(player);

        if (!bPlayer.canBend(this) || hasAbility(player, SpiritProjection.class)) {
            return;
        }

        setFields();

        // Initialize charging state
        this.startLocation = player.getLocation().clone();
        this.originalGameMode = player.getGameMode();
        this.chargeStartTime = System.currentTimeMillis();
        this.isActive = false; // Not active until charged and released
        this.isCharging = true;
        this.isCharged = false;
        this.hasDarknessEffect = false;
        this.lastLocation = player.getLocation().clone();
        this.lastDistance = 0.0;



        start();
    }
    
    private void setFields() {
        int statLevel = StatisticsMethods.getId("AbilityLevel_" + this.getName());
        this.currentLevel = TLBMethods.limitLevels(player, statLevel);

        this.cooldown = TLBMethods.getLong("ExtraAbilities.Air.Spiritualist.SpiritProjection.Cooldown", currentLevel);
        this.maxDistance = TLBMethods.getDouble("ExtraAbilities.Air.Spiritualist.SpiritProjection.MaxDistance", currentLevel);
        this.maxDuration = TLBMethods.getLong("ExtraAbilities.Air.Spiritualist.SpiritProjection.MaxDuration", currentLevel);
        this.chargeTime = TLBMethods.getLong("ExtraAbilities.Air.Spiritualist.SpiritProjection.ChargeTime", currentLevel);
        this.minSpeedPercent = TLBMethods.getDouble("ExtraAbilities.Air.Spiritualist.SpiritProjection.MinSpeedPercent", currentLevel);
        this.startingFlySpeed = (float) TLBMethods.getDouble("ExtraAbilities.Air.Spiritualist.SpiritProjection.StartingFlySpeed", currentLevel);
        this.maxVerticalRange = TLBMethods.getDouble("ExtraAbilities.Air.Spiritualist.SpiritProjection.MaxVerticalRange", currentLevel);
    }
    
    @Override
    public void progress() {
        if (!player.isOnline() || player.isDead()) {
            endProjection();
            return;
        }

        if (isCharging) {
            // Handle charging phase
            if (!player.isSneaking()) {
                // Player released sneak - check if charged
                if (isCharged) {
                    // Start the projection
                    startProjection();
                } else {
                    // Not charged enough, remove ability
                    remove();
                }
                return;
            }

            // Check if charge time is complete
            long chargeElapsed = System.currentTimeMillis() - chargeStartTime;
            if (chargeElapsed >= chargeTime && !isCharged) {
                isCharged = true;
    
            }

            // Display charging particles
            displayChargingParticles(chargeElapsed);

        } else if (isActive) {
            // Handle active projection phase

            // Check time limit (3 seconds)
            if (System.currentTimeMillis() - startTime > maxDuration) {
    
                endProjection();
                return;
            }

            // Check distance limit
            if (player.getLocation().distance(startLocation) > maxDistance) {
    
                endProjection();
                return;
            }

            // Ensure player stays in spectator mode
            if (player.getGameMode() != GameMode.SPECTATOR) {
                player.setGameMode(GameMode.SPECTATOR);
            }

            // Update player speed and effects based on distance from start
            updatePlayerSpeed();
            updateDarknessEffect();

            // Prevent vertical movement - keep player at starting Y level
            restrictVerticalMovement();
        }
    }
    
    private void startProjection() {
        isCharging = false;
        isActive = true;
        startTime = System.currentTimeMillis();

        // Store original fly speed before changing to spectator
        originalFlySpeed = player.getFlySpeed();

        // Set player to spectator mode
        player.setGameMode(GameMode.SPECTATOR);

        // Set the starting fly speed
        player.setFlySpeed(startingFlySpeed);

        // Apply enderman vision using Denizen command
        applyEndermanVision();

        // Spawn NPC body at start location
        spawnBodyNPC();
    }

    private void displayChargingParticles(long chargeElapsed) {
        Location playerLoc = player.getLocation().add(0, 1, 0);

        // Calculate charge progress (0.0 to 1.0)
        double progress = Math.min(1.0, (double) chargeElapsed / chargeTime);

        // Display different particles based on charge progress
        if (progress < 0.5) {
            // Early charging - small white particles
            ParticleEffect.CLOUD.display(playerLoc, 1, 0.1, 0.1, 0.1, 0.01);
        } else if (progress < 1.0) {
            // Mid charging - more particles, slightly faster
            ParticleEffect.CLOUD.display(playerLoc, 2, 0.5, 0.5, 0.5, 0.02);
            ParticleEffect.SPELL.display(playerLoc, 2, 0.2, 0.2, 0.2, 0.0);
        } else {
            // Fully charged - bright particles indicating ready
            ParticleEffect.SPELL.display(playerLoc, 8, 0.7, 0.7, 0.7, 0.0);
            ParticleEffect.ENCHANTMENT_TABLE.display(playerLoc, 5, 0.5, 0.5, 0.5, 0.0);
            ParticleEffect.PORTAL.display(playerLoc, 3, 0.3, 0.3, 0.3, 0.0);
        }
    }

    private void updatePlayerSpeed() {
        // Calculate current distance from start location
        double currentDistance = player.getLocation().distance(startLocation);

        // Determine if player is moving away from or towards the start location
        boolean movingAway = currentDistance > lastDistance;

        // Calculate distance ratio (0.0 at start, 1.0 at max distance)
        double distanceRatio = Math.min(1.0, currentDistance / maxDistance);

        // Speed reduction only applies when moving away from start location
        double speedMultiplier;
        if (!movingAway) {
            // Moving towards start - always full speed
            speedMultiplier = 1.0;
        } else {
            // More gradual speed reduction across the entire range when moving away
            // Use a gentler exponential curve that starts reducing speed earlier but more gradually
            double minSpeedDecimal = minSpeedPercent / 100.0;

            // Use a smaller k value for more gradual reduction
            double k = 2.0; // Much gentler than the previous calculation
            speedMultiplier = Math.exp(-k * distanceRatio);

            // Ensure we don't go below the minimum speed
            speedMultiplier = Math.max(minSpeedDecimal, speedMultiplier);
        }

        // Apply speed multiplier with the configured minimum speed
        double minSpeedDecimal = minSpeedPercent / 100.0;
        float newSpeed = (float) Math.max(minSpeedDecimal, originalFlySpeed * speedMultiplier);
        player.setFlySpeed(newSpeed);

        // Update tracking variables for next iteration
        lastLocation = player.getLocation().clone();
        lastDistance = currentDistance;


    }

    private void applyEndermanVision() {
        try {
            // Use Denizen API to apply enderman vision
            PlayerTag denizenPlayer = new PlayerTag(player);
            Mechanism visionMechanism = new Mechanism("vision", new ElementTag("enderman"), null);
            denizenPlayer.adjust(visionMechanism);


        } catch (Exception e) {

        }
    }

    private void removeEndermanVision() {
        try {
            // Use Denizen API to clear the vision effect (remove any special vision)
            PlayerTag denizenPlayer = new PlayerTag(player);
            Mechanism visionMechanism = new Mechanism("vision", new ElementTag(""), null);
            denizenPlayer.adjust(visionMechanism);


        } catch (Exception e) {

        }
    }

    private void spawnBodyNPC() {
        try {
            // Get Citizens registry
            NPCRegistry registry = CitizensAPI.getNPCRegistry();

            // Store player's current yaw (facing direction)
            float playerYaw = player.getLocation().getYaw();

            // Create NPC at player's current location
            Location npcLocation = player.getLocation().clone();
            String npcName = ""; // Use empty name to keep it hidden

            // Create the NPC with empty name
            bodyNPC = registry.createNPC(org.bukkit.entity.EntityType.PLAYER, npcName);

            // Configure the NPC
            if (bodyNPC != null) {
                // Set the NPC's skin to match the player
                SkinTrait skinTrait = bodyNPC.getOrAddTrait(SkinTrait.class);
                skinTrait.setSkinName(player.getName());

                // Hide the name tag using multiple methods to ensure it's hidden
                bodyNPC.data().set("nameplate-visible", false);
                bodyNPC.setName(""); // Keep name empty to hide it

                // Make the NPC vulnerable to damage
                bodyNPC.setProtected(false);

                // Spawn the NPC at the location
                bodyNPC.spawn(npcLocation);

                // Additional configuration to ensure NPC can be damaged
                if (bodyNPC.getEntity() != null) {
                    bodyNPC.getEntity().setInvulnerable(false);
                    // Keep the name empty - don't set a visible name
                }

                // Set the NPC's rotation to match the player's facing direction
                if (bodyNPC.getEntity() != null) {
                    Location entityLoc = bodyNPC.getEntity().getLocation();
                    entityLoc.setYaw(playerYaw);
                    entityLoc.setPitch(0); // Keep level pitch
                    bodyNPC.getEntity().teleport(entityLoc);
                }

                // Make the NPC sit
                SitTrait sitTrait = bodyNPC.getOrAddTrait(SitTrait.class);
                sitTrait.setSitting(npcLocation);

                // Register this NPC in our tracking map
                npcToPlayerMap.put(bodyNPC, player);
            }

        } catch (Exception e) {

        }
    }

    private void removeBodyNPC() {
        try {
            if (bodyNPC != null) {
                // Remove from tracking map
                npcToPlayerMap.remove(bodyNPC);

                // Destroy the NPC using Citizens API
                bodyNPC.destroy();
                bodyNPC = null;
            }
        } catch (Exception e) {
            // Ignore errors during cleanup
        }
    }

    private void restrictVerticalMovement() {
        Location currentLoc = player.getLocation();
        double currentY = currentLoc.getY();
        double startY = startLocation.getY();
        double verticalDistance = currentY - startY;

        // Allow movement within configured range up or down from starting position

        if (Math.abs(verticalDistance) > maxVerticalRange) {
            // Calculate the corrected Y position (limit to 2 blocks in either direction)
            double correctedY;
            if (verticalDistance > maxVerticalRange) {
                // Player is too high, limit to 2 blocks above start
                correctedY = startY + maxVerticalRange;
            } else {
                // Player is too low, limit to 2 blocks below start
                correctedY = startY - maxVerticalRange;
            }

            // Create new location with corrected Y coordinate but current X and Z
            Location correctedLoc = new Location(
                currentLoc.getWorld(),
                currentLoc.getX(),
                correctedY,
                currentLoc.getZ(),
                currentLoc.getYaw(),
                currentLoc.getPitch()
            );

            // Teleport player back to within allowed vertical range
            player.teleport(correctedLoc);


        }
    }

    private void updateDarknessEffect() {
        // Calculate current distance from start location
        double currentDistance = player.getLocation().distance(startLocation);

        // Calculate distance ratio (0.0 at start, 1.0 at max distance)
        double distanceRatio = currentDistance / maxDistance;

        // Check if player is in the last 10% of max distance (90% or more of max distance)
        boolean shouldHaveDarkness = distanceRatio >= 0.90;

        if (shouldHaveDarkness && !hasDarknessEffect) {
            // Apply darkness effect
            player.addPotionEffect(new PotionEffect(PotionEffectType.DARKNESS, Integer.MAX_VALUE, 0, false, false));
            hasDarknessEffect = true;

        } else if (!shouldHaveDarkness && hasDarknessEffect) {
            // Remove darkness effect
            player.removePotionEffect(PotionEffectType.DARKNESS);
            hasDarknessEffect = false;

        }
    }

    public void endProjection() {
        if (!isActive) return;

        isActive = false;

        // Ensure player is valid and online before cleanup
        if (player == null || !player.isOnline()) {
            return;
        }

        // Remove darkness effect if it's active
        if (hasDarknessEffect) {
            try {
                player.removePotionEffect(PotionEffectType.DARKNESS);
                hasDarknessEffect = false;
            } catch (Exception e) {
                // Ignore errors during cleanup
            }
        }

        // Remove enderman vision
        removeEndermanVision();

        // Remove body NPC
        removeBodyNPC();

        // Always teleport player back to start location first (with safety checks)
        if (startLocation != null) {
            try {
                player.teleport(startLocation);
            } catch (Exception e) {
                // Ignore teleport errors
            }
        }

        // Restore original game mode and fly speed (with safety checks)
        try {
            player.setGameMode(originalGameMode);
            player.setFlySpeed(originalFlySpeed); // Reset speed to original
        } catch (Exception e) {
            // Fallback to survival mode if restoration fails
            try {
                player.setGameMode(GameMode.SURVIVAL);
                player.setFlySpeed(0.1f); // Default fly speed
            } catch (Exception fallbackError) {
                // Ignore fallback errors
            }
        }

        // Add cooldown
        if (bPlayer != null) {
            bPlayer.addCooldown(this);
        }

        // Remove the ability
        remove();
    }
    
    @Override
    public boolean isSneakAbility() {
        return true;
    }
    
    @Override
    public boolean isHarmlessAbility() {
        return true;
    }
    
    @Override
    public long getCooldown() {
        return cooldown;
    }
    
    @Override
    public Location getLocation() {
        return startLocation;
    }
    
    @Override
    public String getName() {
        return "SpiritProjection";
    }
    
    @Override
    public String getDescription() {
        return "Allows the spiritualist to project their spirit, entering the spirit world to scout ahead. " +
               "The projection ends if you travel too far, spend too long in the spirit world, if you take damage, or click your body.";
    }

    @Override
    public String getInstructions() {
        return "Hold sneak to charge the ability. Release sneak when fully charged to enter spirit projection mode.";
    }
    
    @Override
    public String getAuthor() {
        return "TLB";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public void load() {
        // Register event listener
        ProjectKorra.plugin.getServer().getPluginManager().registerEvents(new SpiritProjectionListener(), ProjectKorra.plugin);
        
        // Add default configuration values
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.Cooldown", 10000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.MaxDistance", 025.0);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.MaxDuration", 4000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.ChargeTime", 3000);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.MinSpeedPercent", 0.5);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.StartingFlySpeed", 0.2);
        ConfigManager.getConfig().addDefault("ExtraAbilities.Air.Spiritualist.SpiritProjection.MaxVerticalRange", 2.0);
        
        ConfigManager.defaultConfig.save();
        ProjectKorra.log.info("Enabled " + this.getName() + " by " + this.getAuthor());
    }
    
    @Override
    public void stop() {
        ProjectKorra.log.info("Disabled " + this.getName() + " by " + this.getAuthor());
    }
    
    public boolean isProjectionActive() {
        return isActive;
    }

    // Static method to get the player associated with an NPC
    public static Player getPlayerForNPC(NPC npc) {
        return npcToPlayerMap.get(npc);
    }

    @Override
    public void remove() {
        // Ensure cleanup when ability is removed (including logout scenarios)
        if (player != null && player.isOnline()) {
            // Remove darkness effect if it's active
            if (hasDarknessEffect) {
                player.removePotionEffect(PotionEffectType.DARKNESS);
                hasDarknessEffect = false;
            }

            // Remove enderman vision
            removeEndermanVision();

            // Remove body NPC
            removeBodyNPC();



            // Restore original game mode and fly speed if we're in an active projection
            if (isActive) {
                // Always teleport back to start location first
                if (startLocation != null) {
                    player.teleport(startLocation);
                }

                player.setGameMode(originalGameMode);
                player.setFlySpeed(originalFlySpeed);
            }
        }

        // Call parent remove method
        super.remove();
    }
}
